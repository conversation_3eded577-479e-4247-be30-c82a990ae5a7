/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Chats` | `/Chats`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Home` | `/Home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Listings` | `/Listings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Profile` | `/Profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Progress` | `/Progress`; params?: Router.UnknownInputParams; } | { pathname: `/auth/ForgotPassword`; params?: Router.UnknownInputParams; } | { pathname: `/auth/Login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/SignUp`; params?: Router.UnknownInputParams; } | { pathname: `/auth/VerifyOTP`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/AadhaarStep`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/BrokerForm`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/BrokerSuccess`; params?: Router.UnknownInputParams; } | { pathname: `/Broker`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/styles`; params?: Router.UnknownInputParams; } | { pathname: `/Components/HeroSection`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Chats/ChatScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Chats/Users`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/Brokers`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/Contractors`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/FabModal`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/Header`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/OngoingProjects`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/OptionModal`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/QuickAccess`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/Sites`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/SupportQuickAccess`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Profile/DocumentPreviewModal`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Profile/Profile`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/BackButton`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/Card`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/CategorySection`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/SearchBar`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/ToastConfig`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Applications`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Hiring`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Payments`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Settings`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Tickets`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/FilePicker`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/FormContext`; params?: Router.UnknownInputParams; } | { pathname: `/Sites`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/InputField`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/Map`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/MapSelector`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/AddSites/EncumbranceScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/AddSites/LocationScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/AddSites/PropertyTaxScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/AddSites/SiteDetailsScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/AddSites/SiteFormNavigator`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/AddSites/SubmitScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/SiteList/SiteDetailsScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/SiteList/SiteList`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/UpdateSite/EncumbranceScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/UpdateSite/LocationScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/UpdateSite/PropertyTaxScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/UpdateSite/SiteDetailsScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/UpdateSite/SiteFormNavigator`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/UpdateSite/SubmitScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Chats/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/Chats` | `/Chats`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/Home` | `/Home`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/Listings` | `/Listings`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/Profile` | `/Profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/Progress` | `/Progress`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/ForgotPassword`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/Login`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/SignUp`; params?: Router.UnknownOutputParams; } | { pathname: `/auth/VerifyOTP`; params?: Router.UnknownOutputParams; } | { pathname: `/Broker/AadhaarStep`; params?: Router.UnknownOutputParams; } | { pathname: `/Broker/BrokerForm`; params?: Router.UnknownOutputParams; } | { pathname: `/Broker/BrokerSuccess`; params?: Router.UnknownOutputParams; } | { pathname: `/Broker`; params?: Router.UnknownOutputParams; } | { pathname: `/Broker/styles`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/HeroSection`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Chats/ChatScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Chats/Users`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Home/Brokers`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Home/Contractors`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Home/FabModal`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Home/Header`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Home/OngoingProjects`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Home/OptionModal`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Home/QuickAccess`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Home/Sites`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Home/SupportQuickAccess`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Profile/DocumentPreviewModal`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Profile/Profile`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Shared/BackButton`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Shared/Card`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Shared/CategorySection`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Shared/SearchBar`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Shared/ToastConfig`; params?: Router.UnknownOutputParams; } | { pathname: `/Profile/Applications`; params?: Router.UnknownOutputParams; } | { pathname: `/Profile/Hiring`; params?: Router.UnknownOutputParams; } | { pathname: `/Profile/Payments`; params?: Router.UnknownOutputParams; } | { pathname: `/Profile/Settings`; params?: Router.UnknownOutputParams; } | { pathname: `/Profile/Tickets`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/FilePicker`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/FormContext`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/InputField`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/Map`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/MapSelector`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/AddSites/EncumbranceScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/AddSites/LocationScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/AddSites/PropertyTaxScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/AddSites/SiteDetailsScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/AddSites/SiteFormNavigator`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/AddSites/SubmitScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/SiteList/SiteDetailsScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/SiteList/SiteList`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/UpdateSite/EncumbranceScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/UpdateSite/LocationScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/UpdateSite/PropertyTaxScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/UpdateSite/SiteDetailsScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/UpdateSite/SiteFormNavigator`; params?: Router.UnknownOutputParams; } | { pathname: `/Sites/UpdateSite/SubmitScreen`; params?: Router.UnknownOutputParams; } | { pathname: `/Components/Chats/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/Chats${`?${string}` | `#${string}` | ''}` | `/Chats${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/Home${`?${string}` | `#${string}` | ''}` | `/Home${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/Listings${`?${string}` | `#${string}` | ''}` | `/Listings${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/Profile${`?${string}` | `#${string}` | ''}` | `/Profile${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/Progress${`?${string}` | `#${string}` | ''}` | `/Progress${`?${string}` | `#${string}` | ''}` | `/auth/ForgotPassword${`?${string}` | `#${string}` | ''}` | `/auth/Login${`?${string}` | `#${string}` | ''}` | `/auth/SignUp${`?${string}` | `#${string}` | ''}` | `/auth/VerifyOTP${`?${string}` | `#${string}` | ''}` | `/Broker/AadhaarStep${`?${string}` | `#${string}` | ''}` | `/Broker/BrokerForm${`?${string}` | `#${string}` | ''}` | `/Broker/BrokerSuccess${`?${string}` | `#${string}` | ''}` | `/Broker${`?${string}` | `#${string}` | ''}` | `/Broker/styles${`?${string}` | `#${string}` | ''}` | `/Components/HeroSection${`?${string}` | `#${string}` | ''}` | `/Components/Chats/ChatScreen${`?${string}` | `#${string}` | ''}` | `/Components/Chats/Users${`?${string}` | `#${string}` | ''}` | `/Components/Home/Brokers${`?${string}` | `#${string}` | ''}` | `/Components/Home/Contractors${`?${string}` | `#${string}` | ''}` | `/Components/Home/FabModal${`?${string}` | `#${string}` | ''}` | `/Components/Home/Header${`?${string}` | `#${string}` | ''}` | `/Components/Home/OngoingProjects${`?${string}` | `#${string}` | ''}` | `/Components/Home/OptionModal${`?${string}` | `#${string}` | ''}` | `/Components/Home/QuickAccess${`?${string}` | `#${string}` | ''}` | `/Components/Home/Sites${`?${string}` | `#${string}` | ''}` | `/Components/Home/SupportQuickAccess${`?${string}` | `#${string}` | ''}` | `/Components/Profile/DocumentPreviewModal${`?${string}` | `#${string}` | ''}` | `/Components/Profile/Profile${`?${string}` | `#${string}` | ''}` | `/Components/Shared/BackButton${`?${string}` | `#${string}` | ''}` | `/Components/Shared/Card${`?${string}` | `#${string}` | ''}` | `/Components/Shared/CategorySection${`?${string}` | `#${string}` | ''}` | `/Components/Shared/SearchBar${`?${string}` | `#${string}` | ''}` | `/Components/Shared/ToastConfig${`?${string}` | `#${string}` | ''}` | `/Profile/Applications${`?${string}` | `#${string}` | ''}` | `/Profile/Hiring${`?${string}` | `#${string}` | ''}` | `/Profile/Payments${`?${string}` | `#${string}` | ''}` | `/Profile/Settings${`?${string}` | `#${string}` | ''}` | `/Profile/Tickets${`?${string}` | `#${string}` | ''}` | `/Sites/FilePicker${`?${string}` | `#${string}` | ''}` | `/Sites/FormContext${`?${string}` | `#${string}` | ''}` | `/Sites${`?${string}` | `#${string}` | ''}` | `/Sites/InputField${`?${string}` | `#${string}` | ''}` | `/Sites/Map${`?${string}` | `#${string}` | ''}` | `/Sites/MapSelector${`?${string}` | `#${string}` | ''}` | `/Sites/AddSites/EncumbranceScreen${`?${string}` | `#${string}` | ''}` | `/Sites/AddSites/LocationScreen${`?${string}` | `#${string}` | ''}` | `/Sites/AddSites/PropertyTaxScreen${`?${string}` | `#${string}` | ''}` | `/Sites/AddSites/SiteDetailsScreen${`?${string}` | `#${string}` | ''}` | `/Sites/AddSites/SiteFormNavigator${`?${string}` | `#${string}` | ''}` | `/Sites/AddSites/SubmitScreen${`?${string}` | `#${string}` | ''}` | `/Sites/SiteList/SiteDetailsScreen${`?${string}` | `#${string}` | ''}` | `/Sites/SiteList/SiteList${`?${string}` | `#${string}` | ''}` | `/Sites/UpdateSite/EncumbranceScreen${`?${string}` | `#${string}` | ''}` | `/Sites/UpdateSite/LocationScreen${`?${string}` | `#${string}` | ''}` | `/Sites/UpdateSite/PropertyTaxScreen${`?${string}` | `#${string}` | ''}` | `/Sites/UpdateSite/SiteDetailsScreen${`?${string}` | `#${string}` | ''}` | `/Sites/UpdateSite/SiteFormNavigator${`?${string}` | `#${string}` | ''}` | `/Sites/UpdateSite/SubmitScreen${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Chats` | `/Chats`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Home` | `/Home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Listings` | `/Listings`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Profile` | `/Profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/Progress` | `/Progress`; params?: Router.UnknownInputParams; } | { pathname: `/auth/ForgotPassword`; params?: Router.UnknownInputParams; } | { pathname: `/auth/Login`; params?: Router.UnknownInputParams; } | { pathname: `/auth/SignUp`; params?: Router.UnknownInputParams; } | { pathname: `/auth/VerifyOTP`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/AadhaarStep`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/BrokerForm`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/BrokerSuccess`; params?: Router.UnknownInputParams; } | { pathname: `/Broker`; params?: Router.UnknownInputParams; } | { pathname: `/Broker/styles`; params?: Router.UnknownInputParams; } | { pathname: `/Components/HeroSection`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Chats/ChatScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Chats/Users`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/Brokers`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/Contractors`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/FabModal`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/Header`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/OngoingProjects`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/OptionModal`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/QuickAccess`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/Sites`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Home/SupportQuickAccess`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Profile/DocumentPreviewModal`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Profile/Profile`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/BackButton`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/Card`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/CategorySection`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/SearchBar`; params?: Router.UnknownInputParams; } | { pathname: `/Components/Shared/ToastConfig`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Applications`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Hiring`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Payments`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Settings`; params?: Router.UnknownInputParams; } | { pathname: `/Profile/Tickets`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/FilePicker`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/FormContext`; params?: Router.UnknownInputParams; } | { pathname: `/Sites`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/InputField`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/Map`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/MapSelector`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/AddSites/EncumbranceScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/AddSites/LocationScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/AddSites/PropertyTaxScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/AddSites/SiteDetailsScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/AddSites/SiteFormNavigator`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/AddSites/SubmitScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/SiteList/SiteDetailsScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/SiteList/SiteList`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/UpdateSite/EncumbranceScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/UpdateSite/LocationScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/UpdateSite/PropertyTaxScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/UpdateSite/SiteDetailsScreen`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/UpdateSite/SiteFormNavigator`; params?: Router.UnknownInputParams; } | { pathname: `/Sites/UpdateSite/SubmitScreen`; params?: Router.UnknownInputParams; } | `/Components/Chats/${Router.SingleRoutePart<T>}${`?${string}` | `#${string}` | ''}` | { pathname: `/Components/Chats/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
