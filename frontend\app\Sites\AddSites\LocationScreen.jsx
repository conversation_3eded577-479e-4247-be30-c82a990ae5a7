import React, { useContext } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    Image,
    KeyboardAvoidingView,
    Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import InputField from '../InputField';
import MapSelector from '../MapSelector';
import BackButton from '../../Components/Shared/BackButton';
import { FormContext, createStyles } from './SiteFormNavigator';

const LocationScreen = ({ navigation }) => {
    const {
        fields,
        setFields,
        region,
        setRegion,
        marker,
        setMarker,
        hasPermission,
        setHasPermission,
        errors,
        theme,
    } = useContext(FormContext);
    const styles = createStyles(theme);

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <ScrollView
                contentContainerStyle={styles.scrollContainer}
                showsVerticalScrollIndicator={false}
            >
                <BackButton color={theme.WHITE} />

                {/* Background Image */}
                <View style={styles.backgroundContainer}>
                    <Image
                        source={require('../../../assets/images/background.png')}
                        style={styles.backgroundImage}
                        resizeMode="cover"
                    />
                    <LinearGradient
                        colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                        style={styles.backgroundOverlay}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 0, y: 1 }}
                    />
                </View>

                {/* Content Container */}
                <View style={styles.contentContainer}>
                    {/* Form Container */}
                    <View
                        style={[
                            styles.formContainer,
                            {
                                shadowColor: theme.SHADOW,
                                backgroundColor: theme.CARD,
                            },
                        ]}
                    >
                        <Text style={[styles.title, { color: theme.PRIMARY }]}>
                            Location and Site Map
                        </Text>
                        <Text
                            style={[
                                styles.subtitle,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Set precise location details
                        </Text>

                        {/* Location Details Section */}
                        <Text
                            style={[
                                styles.sectionTitle,
                                { color: theme.PRIMARY },
                            ]}
                        >
                            Location Details
                        </Text>
                        <InputField
                            label="Location"
                            value={fields.location}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, location: t }))
                            }
                            placeholder="Enter Location"
                            icon="location-outline"
                            error={errors?.location}
                        />
                        <MapSelector
                            region={region}
                            setRegion={setRegion}
                            marker={marker}
                            setMarker={setMarker}
                            fields={fields}
                            setFields={setFields}
                            hasPermission={hasPermission}
                            setHasPermission={setHasPermission}
                        />
                        <InputField
                            label="Latitude"
                            value={fields.latitude}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, latitude: t }))
                            }
                            keyboardType="numeric"
                            placeholder="Enter Latitude"
                            icon="navigate-outline"
                            error={errors?.latitude}
                        />
                        <InputField
                            label="Longitude"
                            value={fields.longitude}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, longitude: t }))
                            }
                            keyboardType="numeric"
                            placeholder="Enter Longitude"
                            icon="navigate-outline"
                            error={errors?.longitude}
                        />

                        {/* Navigation Buttons */}
                        <View style={styles.buttonContainer}>
                            <TouchableOpacity
                                style={styles.backButton}
                                onPress={() => navigation.goBack()}
                                activeOpacity={0.8}
                            >
                                <Text
                                    style={[
                                        styles.backButtonText,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Back
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[
                                    styles.nextButton,
                                    { shadowColor: theme.PRIMARY },
                                ]}
                                onPress={() =>
                                    navigation.navigate('Encumbrance')
                                }
                                activeOpacity={0.8}
                            >
                                <LinearGradient
                                    colors={[theme.PRIMARY, theme.SECONDARY]}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 0 }}
                                    style={styles.nextButtonGradient}
                                >
                                    <Text
                                        style={[
                                            styles.nextButtonText,
                                            { color: theme.WHITE },
                                        ]}
                                    >
                                        Next
                                    </Text>
                                </LinearGradient>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    );
};

export default LocationScreen;
