import React, { useState, createContext, useContext } from 'react';
import { StyleSheet, Platform, Dimensions } from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import SiteDetailsScreen from './SiteDetailsScreen';
import LocationScreen from './LocationScreen';
import EncumbranceScreen from './EncumbranceScreen';
import PropertyTaxScreen from './PropertyTaxScreen';
import SubmitScreen from './SubmitScreen';
import { ThemeContext } from '@react-navigation/native';

const FormContext = createContext();
const Stack = createStackNavigator();
const { height } = Dimensions.get('window');

export default function SiteFormNavigator() {
    const [fields, setFields] = useState({
        name: '',
        addressLine1: '',
        addressLine2: '',
        landmark: '',
        location: '',
        pincode: '',
        state: '',
        district: '',
        plotArea: '',
        price: '',
        latitude: '',
        longitude: '',
        encOwnerName: '',
        encDocumentNo: '',
        surveyNo: '',
        village: '',
        subDistrict: '',
        District: '',
        ptrOwnerName: '',
        ptrReciptNo: '',
        siteImages: [],
        encumbranceCert: null,
        propertyTaxRec: null,
    });

    const [region, setRegion] = useState({
        latitude: 37.78825,
        longitude: -122.4324,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
    });

    const [marker, setMarker] = useState(null);
    const [hasPermission, setHasPermission] = useState(false);
    const { theme } = useContext(ThemeContext);

    return (
        <FormContext.Provider
            value={{
                fields,
                setFields,
                region,
                setRegion,
                marker,
                setMarker,
                hasPermission,
                setHasPermission,
                theme,
            }}
        >
            <Stack.Navigator initialRouteName="SiteDetails">
                <Stack.Screen
                    name="SiteDetails"
                    component={SiteDetailsScreen}
                    options={{ headerTitle: 'Site Details' }}
                />
                <Stack.Screen
                    name="Location"
                    component={LocationScreen}
                    options={{ headerTitle: 'Location and Site Map' }}
                />
                <Stack.Screen
                    name="Encumbrance"
                    component={EncumbranceScreen}
                    options={{ headerTitle: 'Encumbrance Certificate' }}
                />
                <Stack.Screen
                    name="PropertyTax"
                    component={PropertyTaxScreen}
                    options={{ headerTitle: 'Property Tax Receipt' }}
                />
                <Stack.Screen
                    name="Submit"
                    component={SubmitScreen}
                    options={{ headerTitle: 'Review and Submit' }}
                />
            </Stack.Navigator>
        </FormContext.Provider>
    );
}

export const createStyles = (theme) =>
    StyleSheet.create({
        container: {
            flex: 1,
            backgroundColor: theme.BACKGROUND,
        },
        scrollContainer: {
            flexGrow: 1,
        },
        backgroundContainer: {
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: height * 0.6,
            zIndex: -1,
        },
        backgroundImage: {
            width: '100%',
            height: '100%',
        },
        backgroundOverlay: {
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
        },
        contentContainer: {
            flex: 1,
            alignItems: 'center',
            paddingTop: 60,
        },
        formContainer: {
            width: '90%',
            maxWidth: 400,
            borderRadius: 20,
            padding: 24,
            backgroundColor: theme.CARD,
            shadowColor: theme.SHADOW,
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.1,
            shadowRadius: 8,
            elevation: 5,
            marginBottom: 20,
        },
        title: {
            fontSize: 24,
            fontWeight: 'bold',
            marginBottom: 8,
            textAlign: 'center',
            color: theme.PRIMARY,
        },
        subtitle: {
            fontSize: 16,
            marginBottom: 24,
            textAlign: 'center',
            color: theme.TEXT_SECONDARY,
        },
        sectionTitle: {
            fontSize: 18,
            fontWeight: 'bold',
            marginTop: 16,
            marginBottom: 12,
            color: theme.PRIMARY,
        },
        submitButton: {
            borderRadius: 12,
            marginTop: 24,
            shadowColor: theme.PRIMARY,
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 8,
            elevation: 5,
        },
        submitButtonGradient: {
            borderRadius: 12,
            paddingVertical: 16,
            paddingHorizontal: 32,
            alignItems: 'center',
            justifyContent: 'center',
        },
        submitButtonDisabled: {
            opacity: 0.6,
        },
        submitButtonText: {
            fontSize: 18,
            fontWeight: 'bold',
            color: theme.WHITE,
        },
        nextButton: {
            borderRadius: 12,
            marginTop: 16,
            shadowColor: theme.PRIMARY,
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 8,
            elevation: 5,
        },
        nextButtonGradient: {
            borderRadius: 12,
            paddingVertical: 16,
            paddingHorizontal: 32,
            alignItems: 'center',
            justifyContent: 'center',
        },
        nextButtonText: {
            fontSize: 18,
            fontWeight: 'bold',
            color: theme.WHITE,
        },
        backButton: {
            backgroundColor: theme.GRAY_LIGHT,
            borderRadius: 12,
            paddingVertical: 16,
            paddingHorizontal: 32,
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: 16,
        },
        backButtonText: {
            fontSize: 16,
            fontWeight: '600',
            color: theme.TEXT_PRIMARY,
        },
        buttonContainer: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginTop: 24,
            gap: 12,
        },
        reviewText: {
            fontSize: 16,
            color: theme.TEXT_PRIMARY,
            marginBottom: 16,
            lineHeight: 24,
        },
        loadingContainer: {
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
        },
        errorText: {
            color: theme.ERROR,
            fontSize: 14,
            marginTop: 4,
            marginLeft: 4,
        },
    });

export { FormContext };
