import React, { useContext } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    Image,
    KeyboardAvoidingView,
    Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import InputField from '../InputField';
import FilePicker from '../FilePicker';
import BackButton from '../../Components/Shared/BackButton';
import { FormContext, createStyles } from './SiteFormNavigator';

const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB

const EncumbranceScreen = ({ navigation }) => {
    const { fields, setFields, errors, theme } = useContext(FormContext);
    const styles = createStyles(theme);

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <ScrollView
                contentContainerStyle={styles.scrollContainer}
                showsVerticalScrollIndicator={false}
            >
                <BackButton color={theme.WHITE} />

                {/* Background Image */}
                <View style={styles.backgroundContainer}>
                    <Image
                        source={require('../../../assets/images/background.png')}
                        style={styles.backgroundImage}
                        resizeMode="cover"
                    />
                    <LinearGradient
                        colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                        style={styles.backgroundOverlay}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 0, y: 1 }}
                    />
                </View>

                {/* Content Container */}
                <View style={styles.contentContainer}>
                    {/* Form Container */}
                    <View
                        style={[
                            styles.formContainer,
                            {
                                shadowColor: theme.SHADOW,
                                backgroundColor: theme.CARD,
                            },
                        ]}
                    >
                        <Text style={[styles.title, { color: theme.PRIMARY }]}>
                            Encumbrance Certificate
                        </Text>
                        <Text
                            style={[
                                styles.subtitle,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Provide encumbrance certificate details
                        </Text>

                        {/* Encumbrance Details Section */}
                        <Text
                            style={[
                                styles.sectionTitle,
                                { color: theme.PRIMARY },
                            ]}
                        >
                            Encumbrance Details
                        </Text>
                        <InputField
                            label="Owner name"
                            value={fields.encOwnerName}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, encOwnerName: t }))
                            }
                            placeholder="Enter Owner name"
                            icon="person-outline"
                            error={errors?.encOwnerName}
                        />
                        <InputField
                            label="Document No."
                            value={fields.encDocumentNo}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, encDocumentNo: t }))
                            }
                            placeholder="Enter Document No."
                            icon="document-text-outline"
                            error={errors?.encDocumentNo}
                        />
                        <InputField
                            label="Survey No."
                            value={fields.surveyNo}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, surveyNo: t }))
                            }
                            placeholder="Enter Survey No."
                            icon="map-outline"
                            error={errors?.surveyNo}
                        />
                        <InputField
                            label="Village"
                            value={fields.village}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, village: t }))
                            }
                            placeholder="Enter Village"
                            icon="home-outline"
                            error={errors?.village}
                        />
                        <InputField
                            label="Sub-District"
                            value={fields.subDistrict}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, subDistrict: t }))
                            }
                            placeholder="Enter Sub-District"
                            icon="location-outline"
                            error={errors?.subDistrict}
                        />
                        <InputField
                            label="District"
                            value={fields.District}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, District: t }))
                            }
                            placeholder="Enter District"
                            icon="map-outline"
                            error={errors?.District}
                        />
                        <FilePicker
                            label="Pick encumbrance certificate"
                            files={fields.encumbranceCert}
                            setFiles={setFields}
                            keyName="encumbranceCert"
                            allowedTypes={ALLOWED_TYPES}
                            maxFileSize={MAX_FILE_SIZE}
                        />

                        {/* Navigation Buttons */}
                        <View style={styles.buttonContainer}>
                            <TouchableOpacity
                                style={styles.backButton}
                                onPress={() => navigation.goBack()}
                                activeOpacity={0.8}
                            >
                                <Text
                                    style={[
                                        styles.backButtonText,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Back
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[
                                    styles.nextButton,
                                    { shadowColor: theme.PRIMARY },
                                ]}
                                onPress={() =>
                                    navigation.navigate('PropertyTax')
                                }
                                activeOpacity={0.8}
                            >
                                <LinearGradient
                                    colors={[theme.PRIMARY, theme.SECONDARY]}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 0 }}
                                    style={styles.nextButtonGradient}
                                >
                                    <Text
                                        style={[
                                            styles.nextButtonText,
                                            { color: theme.WHITE },
                                        ]}
                                    >
                                        Next
                                    </Text>
                                </LinearGradient>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    );
};

export default EncumbranceScreen;
