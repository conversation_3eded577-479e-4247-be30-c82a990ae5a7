import { View, Text, Image, TouchableOpacity, Animated } from 'react-native';
import React, { useContext } from 'react';
import Ionicons from '@expo/vector-icons/Ionicons';
import { ThemeContext } from '../../../context/ThemeContext';

export default function Card({
    title,
    name,
    location,
    price,
    image,
    verified,
    rating,
    projects,
    onPress,
    onInterestedPress,
    accessibilityLabel,
}) {
    const { theme } = useContext(ThemeContext);
    const scaleAnim = React.useRef(new Animated.Value(1)).current;

    const handlePressIn = () => {
        Animated.spring(scaleAnim, {
            toValue: 0.98,
            friction: 8,
            tension: 100,
            useNativeDriver: true,
        }).start();
    };

    const handlePressOut = () => {
        Animated.spring(scaleAnim, {
            toValue: 1,
            friction: 8,
            tension: 100,
            useNativeDriver: true,
        }).start();
    };

    return (
        <TouchableOpacity
            className="bg-white rounded-xl overflow-hidden shadow-md mr-4 w-64"
            onPress={onPress}
            onPressIn={handlePressIn}
            onPressOut={handlePressOut}
            activeOpacity={0.9}
            accessibilityLabel={accessibilityLabel}
        >
            <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
                {/* Image Section */}
                <View className="relative">
                    <Image
                        source={image}
                        className="w-full h-48 rounded-t-xl"
                        resizeMode="cover"
                    />
                    <View className="absolute bottom-0 left-0 right-0 bg-black/50 p-2">
                        <Text className="text-white text-base font-bold">
                            {title || name}
                        </Text>
                    </View>
                </View>

                {/* Content Section */}
                <View className="p-3">
                    {location && (
                        <View className="flex-row items-center mt-1">
                            <Ionicons
                                name="location-outline"
                                size={14}
                                color={theme.GRAY}
                            />
                            <Text className="text-gray-500 text-sm ml-1">
                                {location}
                            </Text>
                        </View>
                    )}
                    {price && (
                        <Text className="text-primary font-bold mt-1">
                            {price}
                        </Text>
                    )}
                    {verified && (
                        <View className="flex-row items-center mt-1">
                            <Ionicons
                                name="checkmark-circle"
                                size={16}
                                color={theme.PRIMARY}
                            />
                            <Text className="text-primary text-sm ml-1">
                                Verified by Site Scout
                            </Text>
                        </View>
                    )}
                    {rating && (
                        <View className="flex-row items-center mt-1">
                            <Ionicons
                                name="star"
                                size={14}
                                color={theme.PRIMARY}
                            />
                            <Text className="text-primary font-bold ml-1">
                                {rating}
                            </Text>
                        </View>
                    )}
                    {projects && (
                        <Text className="text-gray-500 text-sm mt-1">
                            {projects} Projects
                        </Text>
                    )}
                    {onInterestedPress && (
                        <TouchableOpacity
                            className="bg-primary px-4 py-2 rounded-lg mt-3"
                            onPress={onInterestedPress}
                            accessibilityLabel={`Show interest in ${title || name}`}
                        >
                            <Text className="text-white font-semibold text-center">
                                Interested
                            </Text>
                        </TouchableOpacity>
                    )}
                </View>
            </Animated.View>
        </TouchableOpacity>
    );
}
