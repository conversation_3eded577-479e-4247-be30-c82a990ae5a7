import React, { useState, useContext } from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    Alert,
    ScrollView,
    Image,
    KeyboardAvoidingView,
    Platform,
    ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { privateAPIClient } from '../../../api';
import BackButton from '../../Components/Shared/BackButton';
import { FormContext, createStyles } from './SiteFormNavigator';

const buildPart = (asset) => ({
    uri: asset.uri,
    name: asset.name || `file_${Date.now()}`,
    type: asset.mimeType || 'application/octet-stream',
});

const SubmitScreen = ({ navigation }) => {
    const {
        fields,
        setFields,
        region,
        setRegion,
        setMarker,
        setHasPermission,
        theme,
    } = useContext(FormContext);
    const [loading, setLoading] = useState(false);
    const router = useRouter();
    const styles = createStyles(theme);

    const handleSave = async () => {
        if (
            !fields.siteImages?.length ||
            !fields.encumbranceCert ||
            !fields.propertyTaxRec
        ) {
            return Alert.alert(
                'Missing docs',
                'Please attach at least one site image and the other required documents.'
            );
        }
        if (!fields.name || !fields.pincode || !fields.plotArea) {
            return Alert.alert(
                'Missing fields',
                'Name, pincode, plot area are required.'
            );
        }

        const form = new FormData();
        fields.siteImages.forEach((image, index) => {
            form.append('siteImages', buildPart(image));
        });
        form.append(
            'encumbranceCertificate',
            buildPart(fields.encumbranceCert)
        );
        form.append('propertyTaxReceipt', buildPart(fields.propertyTaxRec));

        Object.entries(fields).forEach(([k, v]) => {
            if (
                k !== 'siteImages' &&
                k !== 'encumbranceCert' &&
                k !== 'propertyTaxRec'
            ) {
                form.append(k, String(v ?? ''));
            }
        });

        try {
            setLoading(true);
            await privateAPIClient.post('/site-service/api/v1/sites', form, {
                headers: { 'Content-Type': 'multipart/form-data' },
            });
            Alert.alert('Success', 'Site created!', [
                {
                    text: 'OK',
                    onPress: () => router.replace('/Sites'), // Navigate to /Sites
                },
            ]);
            setFields({
                name: '',
                addressLine1: '',
                addressLine2: '',
                landmark: '',
                location: '',
                pincode: '',
                state: '',
                district: '',
                plotArea: '',
                price: '',
                latitude: '',
                longitude: '',
                encOwnerName: '',
                encDocumentNo: '',
                surveyNo: '',
                village: '',
                subDistrict: '',
                District: '',
                ptrOwnerName: '',
                ptrReciptNo: '',
                siteImages: [],
                encumbranceCert: null,
                propertyTaxRec: null,
            });
            setRegion({
                latitude: 37.78825,
                longitude: -122.4324,
                latitudeDelta: 0.0922,
                longitudeDelta: 0.0421,
            });
            setMarker(null);
            setHasPermission(false);
        } catch (err) {
            console.error(err.response?.data || err);
            Alert.alert('Upload failed', 'Check backend logs for details');
        } finally {
            setLoading(false);
        }
    };

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <ScrollView
                contentContainerStyle={styles.scrollContainer}
                showsVerticalScrollIndicator={false}
            >
                <BackButton color={theme.WHITE} />

                {/* Background Image */}
                <View style={styles.backgroundContainer}>
                    <Image
                        source={require('../../../assets/images/background.png')}
                        style={styles.backgroundImage}
                        resizeMode="cover"
                    />
                    <LinearGradient
                        colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                        style={styles.backgroundOverlay}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 0, y: 1 }}
                    />
                </View>

                {/* Content Container */}
                <View style={styles.contentContainer}>
                    {/* Form Container */}
                    <View
                        style={[
                            styles.formContainer,
                            {
                                shadowColor: theme.SHADOW,
                                backgroundColor: theme.CARD,
                            },
                        ]}
                    >
                        <Text style={[styles.title, { color: theme.PRIMARY }]}>
                            Review and Submit
                        </Text>
                        <Text
                            style={[
                                styles.subtitle,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Review all details before submitting
                        </Text>

                        {/* Review Section */}
                        <Text
                            style={[
                                styles.sectionTitle,
                                { color: theme.PRIMARY },
                            ]}
                        >
                            Confirm Details
                        </Text>
                        <Text
                            style={[
                                styles.reviewText,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                        >
                            Please review all details before submitting. Make
                            sure all required documents are attached and
                            information is accurate.
                        </Text>

                        {/* Navigation Buttons */}
                        <View style={styles.buttonContainer}>
                            <TouchableOpacity
                                style={styles.backButton}
                                onPress={() => navigation.goBack()}
                                activeOpacity={0.8}
                            >
                                <Text
                                    style={[
                                        styles.backButtonText,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Back
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[
                                    styles.submitButton,
                                    { shadowColor: theme.PRIMARY },
                                    loading && styles.submitButtonDisabled,
                                ]}
                                onPress={handleSave}
                                disabled={loading}
                                activeOpacity={0.8}
                            >
                                <LinearGradient
                                    colors={[theme.PRIMARY, theme.SECONDARY]}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 0 }}
                                    style={styles.submitButtonGradient}
                                >
                                    {loading ? (
                                        <View style={styles.loadingContainer}>
                                            <ActivityIndicator
                                                color={theme.WHITE}
                                                size="small"
                                            />
                                            <Text
                                                style={[
                                                    styles.submitButtonText,
                                                    {
                                                        color: theme.WHITE,
                                                        marginLeft: 8,
                                                    },
                                                ]}
                                            >
                                                Uploading...
                                            </Text>
                                        </View>
                                    ) : (
                                        <Text
                                            style={[
                                                styles.submitButtonText,
                                                { color: theme.WHITE },
                                            ]}
                                        >
                                            Save Site
                                        </Text>
                                    )}
                                </LinearGradient>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    );
};

export default SubmitScreen;
