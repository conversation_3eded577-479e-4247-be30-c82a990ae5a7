import React, { useState, useEffect, useContext } from 'react';
import {
    View,
    Text,
    Image,
    TextInput,
    TouchableOpacity,
    StyleSheet,
    Dimensions,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    ActivityIndicator,
    Vibration,
    Pressable,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { privateAPIClient } from '../../api';
import BackButton from '../Components/Shared/BackButton';
import * as DocumentPicker from 'expo-document-picker';
import ModalDatePicker from 'react-native-modal-datetime-picker';
import { Picker } from '@react-native-picker/picker';
import { ThemeContext } from '../../context/ThemeContext';
import { showToast } from '../../utils/showToast';

const { height } = Dimensions.get('window');

export default function BrokerForm() {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const [userData, setUserData] = useState({
        name: '',
        email: '',
        phone: '',
    });
    const [formData, setFormData] = useState({
        aadhaarNumber: '',
        nameOnAadhaar: '',
        dateOfBirth: new Date(),
        gender: '',
        address: '',
        panNumber: '',
        panName: '',
        panDateOfBirth: new Date(),
        experience: '',
        serviceAreas: '',
    });
    const [files, setFiles] = useState({
        aadhaarDocument: null,
        panDocument: null,
    });
    const [errors, setErrors] = useState({
        aadhaarNumber: '',
        nameOnAadhaar: '',
        dateOfBirth: '',
        gender: '',
        address: '',
        panNumber: '',
        panName: '',
        panDateOfBirth: '',
        experience: '',
        serviceAreas: '',
        aadhaarDocument: '',
        panDocument: '',
    });
    const [isLoading, setIsLoading] = useState(false);
    const [isFetchingUser, setIsFetchingUser] = useState(true);
    const [showAadhaarDatePicker, setShowAadhaarDatePicker] = useState(false);
    const [showPanDatePicker, setShowPanDatePicker] = useState(false);

    // Fetch user details
    useEffect(() => {
        const fetchUserDetails = async () => {
            try {
                const response = await privateAPIClient.get(
                    '/user-service/api/v1/user/profile'
                );
                const data = response.data.user;
                setUserData({
                    name: data.name || '',
                    email: data.email || '',
                    phone: data.phone || '',
                });
            } catch (error) {
                showToast('error', 'Error', 'Failed to fetch user details.');
            } finally {
                setIsFetchingUser(false);
            }
        };
        fetchUserDetails();
    }, []);

    // Validate form
    const validateForm = () => {
        let isValid = true;
        const newErrors = {
            aadhaarNumber: '',
            nameOnAadhaar: '',
            dateOfBirth: '',
            gender: '',
            address: '',
            panNumber: '',
            panName: '',
            panDateOfBirth: '',
            experience: '',
            serviceAreas: '',
            aadhaarDocument: '',
            panDocument: '',
        };

        // Aadhaar validation
        if (!formData.aadhaarNumber.match(/^\d{12}$/)) {
            newErrors.aadhaarNumber = 'Enter a valid 12-digit Aadhaar number';
            isValid = false;
        }
        if (!formData.nameOnAadhaar.trim()) {
            newErrors.nameOnAadhaar = 'Name on Aadhaar is required';
            isValid = false;
        }
        if (!formData.dateOfBirth || formData.dateOfBirth > new Date()) {
            newErrors.dateOfBirth = 'Enter a valid date of birth';
            isValid = false;
        }
        if (!['Male', 'Female', 'Other'].includes(formData.gender)) {
            newErrors.gender = 'Select a gender';
            isValid = false;
        }
        if (!formData.address.trim()) {
            newErrors.address = 'Address is required';
            isValid = false;
        }

        // PAN validation
        if (!formData.panNumber.match(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/)) {
            newErrors.panNumber = 'Enter a valid 10-character PAN number';
            isValid = false;
        }
        if (!formData.panName.trim()) {
            newErrors.panName = 'Name on PAN is required';
            isValid = false;
        }
        if (!formData.panDateOfBirth || formData.panDateOfBirth > new Date()) {
            newErrors.panDateOfBirth = 'Enter a valid date of birth';
            isValid = false;
        }

        // Broker validation
        const experienceNum = parseFloat(formData.experience);
        if (isNaN(experienceNum) || experienceNum < 0) {
            newErrors.experience = 'Enter a valid non-negative number';
            isValid = false;
        }
        if (!formData.serviceAreas.trim()) {
            newErrors.serviceAreas = 'Enter at least one service area';
            isValid = false;
        }

        setErrors(newErrors);
        return isValid;
    };

    // Handle text input changes
    const handleChange = (name, value) => {
        setFormData((prev) => ({ ...prev, [name]: value }));
        if (errors[name]) {
            setErrors((prev) => ({ ...prev, [name]: '' }));
        }
    };

    // Handle date changes
    const handleDateChange = (name, date) => {
        setFormData((prev) => ({ ...prev, [name]: date }));
        if (errors[name]) {
            setErrors((prev) => ({ ...prev, [name]: '' }));
        }
        if (name === 'dateOfBirth') setShowAadhaarDatePicker(false);
        else setShowPanDatePicker(false);
    };

    // Handle file selection
    const pickDocument = async (type) => {
        try {
            const result = await DocumentPicker.getDocumentAsync({
                type: ['image/jpeg', 'image/png', 'application/pdf'],
            });
            if (result.assets && result.assets[0]) {
                const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
                if (result.assets[0].size > MAX_FILE_SIZE) {
                    showToast(
                        'error',
                        'File Size Error',
                        'File size exceeds the limit of 5MB.'
                    );
                    return;
                }
                const allowedTypes = [
                    'image/jpeg',
                    'image/png',
                    'application/pdf',
                ];
                if (!allowedTypes.includes(result.assets[0].mimeType)) {
                    showToast(
                        'error',
                        'File Type Error',
                        'Invalid file type. Only JPEG, PNG, and PDF are allowed.'
                    );
                    return;
                }
                setFiles((prev) => ({ ...prev, [type]: result.assets[0] }));
                setErrors((prev) => ({ ...prev, [type]: '' }));
            }
        } catch (error) {
            showToast('error', 'Failed to select file');
        }
    };

    // Handle form submission
    const handleSubmit = async () => {
        if (!validateForm()) {
            Vibration.vibrate(200);
            return;
        }

        setIsLoading(true);
        try {
            const formPayload = new FormData();
            // Append text fields
            formPayload.append('aadhaarNumber', formData.aadhaarNumber);
            formPayload.append('nameOnAadhaar', formData.nameOnAadhaar);
            formPayload.append(
                'dateOfBirth',
                formData.dateOfBirth.toISOString()
            );
            formPayload.append('gender', formData.gender);
            formPayload.append('address', formData.address);
            formPayload.append('panNumber', formData.panNumber);
            formPayload.append('panName', formData.panName);
            formPayload.append(
                'panDateOfBirth',
                formData.panDateOfBirth.toISOString()
            );
            formPayload.append('experience', formData.experience);
            formPayload.append(
                'serviceAreas',
                JSON.stringify(
                    formData.serviceAreas
                        .split(',')
                        .map((area) => area.trim())
                        .filter((area) => area)
                )
            );

            // Append files
            if (files.aadhaarDocument) {
                formPayload.append('aadhaarDocument', {
                    uri: files.aadhaarDocument.uri,
                    name: files.aadhaarDocument.name || 'aadhaarDocument',
                    type:
                        files.aadhaarDocument.mimeType ||
                        'application/octet-stream',
                });
            }
            if (files.panDocument) {
                formPayload.append('panDocument', {
                    uri: files.panDocument.uri,
                    name: files.panDocument.name || 'panDocument',
                    type:
                        files.panDocument.mimeType ||
                        'application/octet-stream',
                });
            }

            await privateAPIClient.post(
                '/user-service/api/v1/brokers',
                formPayload,
                {
                    headers: { 'Content-Type': 'multipart/form-data' },
                }
            );
            router.push('./Success');
        } catch (error) {
            const message =
                error.response?.data?.error || 'Failed to submit application';
            if (error.response?.status === 409) {
                showToast(
                    'error',
                    'Error',
                    'You are already registered as a Site Scout.'
                );
            } else if (error.response?.status === 400) {
                showToast('error', 'Invalid Data', message);
            } else {
                showToast('error', 'something went wrong');
            }
            Vibration.vibrate(200);
        } finally {
            setIsLoading(false);
        }
    };

    if (isFetchingUser) {
        return (
            <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={theme.PRIMARY} />
            </View>
        );
    }

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <ScrollView
                contentContainerStyle={styles.scrollContainer}
                showsVerticalScrollIndicator={false}
            >
                <BackButton color={theme.WHITE} />

                {/* Background Image */}
                <View style={styles.backgroundContainer}>
                    <Image
                        source={require('../../assets/images/background.png')}
                        style={styles.backgroundImage}
                        resizeMode="cover"
                    />
                    <LinearGradient
                        colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                        style={styles.backgroundOverlay}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 0, y: 1 }}
                    />
                </View>

                {/* Content Container */}
                <View style={styles.contentContainer}>
                    {/* Form Container */}
                    <View
                        style={[
                            styles.formContainer,
                            {
                                shadowColor: theme.SHADOW,
                                backgroundColor: theme.CARD,
                            },
                        ]}
                    >
                        <Text style={[styles.title, { color: theme.PRIMARY }]}>
                            Site Scout Application
                        </Text>
                        <Text
                            style={[
                                styles.subtitle,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Apply to become a trusted broker
                        </Text>

                        {/* User Details Section */}
                        <Text
                            style={[
                                styles.sectionTitle,
                                { color: theme.PRIMARY },
                            ]}
                        >
                            Your Details
                        </Text>
                        <View
                            style={[
                                styles.inputContainer,
                                styles.disabledInputContainer,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                            ]}
                        >
                            <Ionicons
                                name="person-outline"
                                size={22}
                                color={theme.PRIMARY}
                                style={styles.inputIcon}
                            />
                            <TextInput
                                value={userData.name}
                                style={[
                                    styles.input,
                                    styles.disabledInput,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                                editable={false}
                                placeholder="Name"
                                accessibilityLabel="User name"
                                testID="name-display"
                            />
                        </View>
                        <View
                            style={[
                                styles.inputContainer,
                                styles.disabledInputContainer,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                            ]}
                        >
                            <Ionicons
                                name="mail-outline"
                                size={22}
                                color={theme.PRIMARY}
                                style={styles.inputIcon}
                            />
                            <TextInput
                                value={userData.email}
                                style={[
                                    styles.input,
                                    styles.disabledInput,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                                editable={false}
                                placeholder="Email"
                                accessibilityLabel="User email"
                                testID="email-display"
                            />
                        </View>
                        <View
                            style={[
                                styles.inputContainer,
                                styles.disabledInputContainer,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                            ]}
                        >
                            <Ionicons
                                name="call-outline"
                                size={22}
                                color={theme.PRIMARY}
                                style={styles.inputIcon}
                            />
                            <TextInput
                                value={userData.phone}
                                style={[
                                    styles.input,
                                    styles.disabledInput,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                                editable={false}
                                placeholder="Phone"
                                accessibilityLabel="User phone"
                                testID="phone-display"
                            />
                        </View>

                        {/* Aadhaar Details Section */}
                        <Text
                            style={[
                                styles.sectionTitle,
                                { color: theme.PRIMARY },
                            ]}
                        >
                            Aadhaar Details
                        </Text>
                        <View
                            style={[
                                styles.inputContainer,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                                errors.aadhaarNumber ? styles.inputError : null,
                            ]}
                        >
                            <MaterialCommunityIcons
                                name="card-account-details-outline"
                                size={22}
                                color={
                                    errors.aadhaarNumber ? 'red' : theme.PRIMARY
                                }
                                style={styles.inputIcon}
                            />
                            <TextInput
                                placeholder="Aadhaar Number"
                                value={formData.aadhaarNumber}
                                onChangeText={(text) =>
                                    handleChange('aadhaarNumber', text)
                                }
                                placeholderTextColor={theme.TEXT_PLACEHOLDER}
                                style={[
                                    styles.input,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                                keyboardType="numeric"
                                maxLength={12}
                                accessibilityLabel="Enter Aadhaar number"
                                testID="aadhaarNumber-input"
                            />
                            {formData.aadhaarNumber.length > 0 && (
                                <TouchableOpacity
                                    onPress={() =>
                                        handleChange('aadhaarNumber', '')
                                    }
                                    style={styles.clearButton}
                                >
                                    <Ionicons
                                        name="close-circle"
                                        size={20}
                                        color="#999"
                                    />
                                </TouchableOpacity>
                            )}
                        </View>
                        {errors.aadhaarNumber && (
                            <Text style={styles.errorText}>
                                {errors.aadhaarNumber}
                            </Text>
                        )}

                        <View
                            style={[
                                styles.inputContainer,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                                errors.nameOnAadhaar ? styles.inputError : null,
                            ]}
                        >
                            <Ionicons
                                name="person-outline"
                                size={22}
                                color={
                                    errors.nameOnAadhaar ? 'red' : theme.PRIMARY
                                }
                                style={styles.inputIcon}
                            />
                            <TextInput
                                placeholder="Name on Aadhaar"
                                value={formData.nameOnAadhaar}
                                onChangeText={(text) =>
                                    handleChange('nameOnAadhaar', text)
                                }
                                placeholderTextColor={theme.TEXT_PLACEHOLDER}
                                style={[
                                    styles.input,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                                accessibilityLabel="Enter name on Aadhaar"
                                testID="nameOnAadhaar-input"
                            />
                            {formData.nameOnAadhaar.length > 0 && (
                                <TouchableOpacity
                                    onPress={() =>
                                        handleChange('nameOnAadhaar', '')
                                    }
                                    style={styles.clearButton}
                                >
                                    <Ionicons
                                        name="close-circle"
                                        size={20}
                                        color="#999"
                                    />
                                </TouchableOpacity>
                            )}
                        </View>
                        {errors.nameOnAadhaar && (
                            <Text style={styles.errorText}>
                                {errors.nameOnAadhaar}
                            </Text>
                        )}

                        <Pressable
                            onPress={() => setShowAadhaarDatePicker(true)}
                            style={[
                                styles.inputContainer,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                                errors.dateOfBirth ? styles.inputError : null,
                            ]}
                        >
                            <Ionicons
                                name="calendar-outline"
                                size={22}
                                color={
                                    errors.dateOfBirth ? 'red' : theme.PRIMARY
                                }
                                style={styles.inputIcon}
                            />
                            <Text
                                style={[
                                    styles.dateText,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                {formData.dateOfBirth
                                    ? formData.dateOfBirth.toLocaleDateString()
                                    : 'Date of Birth'}
                            </Text>
                        </Pressable>
                        <ModalDatePicker
                            isVisible={showAadhaarDatePicker}
                            mode="date"
                            date={formData.dateOfBirth}
                            placeholder="Date of Birth"
                            onConfirm={(date) =>
                                handleDateChange('dateOfBirth', date)
                            }
                            onCancel={() => setShowAadhaarDatePicker(false)}
                            maximumDate={new Date()}
                            testID="aadhaarDatePicker"
                        />
                        {errors.dateOfBirth && (
                            <Text style={styles.errorText}>
                                {errors.dateOfBirth}
                            </Text>
                        )}

                        <View
                            style={[
                                styles.inputContainer,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                                errors.gender ? styles.inputError : null,
                            ]}
                        >
                            <Ionicons
                                name="people-outline"
                                size={22}
                                color={errors.gender ? 'red' : theme.PRIMARY}
                                style={styles.inputIcon}
                            />
                            <Picker
                                selectedValue={formData.gender}
                                onValueChange={(value) =>
                                    handleChange('gender', value)
                                }
                                placeholderTextColor={theme.TEXT_PLACEHOLDER}
                                style={[
                                    styles.picker,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                                accessibilityLabel="Select gender"
                                testID="gender-picker"
                            >
                                <Picker.Item label="Select Gender" value="" />
                                <Picker.Item label="Male" value="Male" />
                                <Picker.Item label="Female" value="Female" />
                                <Picker.Item label="Other" value="Other" />
                            </Picker>
                        </View>
                        {errors.gender && (
                            <Text style={styles.errorText}>
                                {errors.gender}
                            </Text>
                        )}

                        <View
                            style={[
                                styles.inputContainer,
                                styles.addressInputContainer,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                                errors.address ? styles.inputError : null,
                            ]}
                        >
                            <Ionicons
                                name="home-outline"
                                size={22}
                                color={errors.address ? 'red' : theme.PRIMARY}
                                style={styles.inputIcon}
                            />
                            <TextInput
                                placeholder="Address"
                                value={formData.address}
                                onChangeText={(text) =>
                                    handleChange('address', text)
                                }
                                placeholderTextColor={theme.TEXT_PLACEHOLDER}
                                style={[
                                    styles.input,
                                    styles.addressInput,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                                multiline
                                accessibilityLabel="Enter address"
                                testID="address-input"
                            />
                            {formData.address.length > 0 && (
                                <TouchableOpacity
                                    onPress={() => handleChange('address', '')}
                                    style={styles.clearButton}
                                >
                                    <Ionicons
                                        name="close-circle"
                                        size={20}
                                        color="#999"
                                    />
                                </TouchableOpacity>
                            )}
                        </View>
                        {errors.address && (
                            <Text style={styles.errorText}>
                                {errors.address}
                            </Text>
                        )}

                        <TouchableOpacity
                            onPress={() => pickDocument('aadhaarDocument')}
                            style={[
                                styles.fileButton,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                                errors.aadhaarDocument
                                    ? styles.inputError
                                    : null,
                            ]}
                        >
                            <Ionicons
                                name="cloud-upload-outline"
                                size={22}
                                color={theme.PRIMARY}
                                style={styles.inputIcon}
                            />
                            <Text
                                style={[
                                    styles.fileButtonText,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                {files.aadhaarDocument
                                    ? files.aadhaarDocument.name
                                    : 'Upload Aadhaar Document'}
                            </Text>
                        </TouchableOpacity>
                        {errors.aadhaarDocument && (
                            <Text style={styles.errorText}>
                                {errors.aadhaarDocument}
                            </Text>
                        )}

                        {/* PAN Details Section */}
                        <Text
                            style={[
                                styles.sectionTitle,
                                { color: theme.PRIMARY },
                            ]}
                        >
                            PAN Details
                        </Text>
                        <View
                            style={[
                                styles.inputContainer,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                                errors.panNumber ? styles.inputError : null,
                            ]}
                        >
                            <MaterialCommunityIcons
                                name="card-account-details-outline"
                                size={22}
                                color={errors.panNumber ? 'red' : theme.PRIMARY}
                                style={styles.inputIcon}
                            />
                            <TextInput
                                placeholder="PAN Number"
                                value={formData.panNumber}
                                onChangeText={(text) =>
                                    handleChange('panNumber', text)
                                }
                                placeholderTextColor={theme.TEXT_PLACEHOLDER}
                                style={[
                                    styles.input,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                                maxLength={10}
                                autoCapitalize="characters"
                                accessibilityLabel="Enter PAN number"
                                testID="panNumber-input"
                            />
                            {formData.panNumber.length > 0 && (
                                <TouchableOpacity
                                    onPress={() =>
                                        handleChange('panNumber', '')
                                    }
                                    style={styles.clearButton}
                                >
                                    <Ionicons
                                        name="close-circle"
                                        size={20}
                                        color="#999"
                                    />
                                </TouchableOpacity>
                            )}
                        </View>
                        {errors.panNumber && (
                            <Text style={styles.errorText}>
                                {errors.panNumber}
                            </Text>
                        )}

                        <View
                            style={[
                                styles.inputContainer,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                                errors.panName ? styles.inputError : null,
                            ]}
                        >
                            <Ionicons
                                name="person-outline"
                                size={22}
                                color={errors.panName ? 'red' : theme.PRIMARY}
                                style={styles.inputIcon}
                            />
                            <TextInput
                                placeholder="Name on PAN"
                                value={formData.panName}
                                onChangeText={(text) =>
                                    handleChange('panName', text)
                                }
                                placeholderTextColor={theme.TEXT_PLACEHOLDER}
                                style={[
                                    styles.input,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                                accessibilityLabel="Enter name on PAN"
                                testID="panName-input"
                            />
                            {formData.panName.length > 0 && (
                                <TouchableOpacity
                                    onPress={() => handleChange('panName', '')}
                                    style={styles.clearButton}
                                >
                                    <Ionicons
                                        name="close-circle"
                                        size={20}
                                        color="#999"
                                    />
                                </TouchableOpacity>
                            )}
                        </View>
                        {errors.panName && (
                            <Text style={styles.errorText}>
                                {errors.panName}
                            </Text>
                        )}

                        <Pressable
                            onPress={() => setShowPanDatePicker(true)}
                            style={[
                                styles.inputContainer,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                                errors.panDateOfBirth
                                    ? styles.inputError
                                    : null,
                            ]}
                        >
                            <Ionicons
                                name="calendar-outline"
                                size={22}
                                color={
                                    errors.panDateOfBirth
                                        ? 'red'
                                        : theme.PRIMARY
                                }
                                style={styles.inputIcon}
                            />
                            <Text
                                style={[
                                    styles.dateText,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                {formData.panDateOfBirth
                                    ? formData.panDateOfBirth.toLocaleDateString()
                                    : 'Date of Birth'}
                            </Text>
                        </Pressable>
                        <ModalDatePicker
                            isVisible={showPanDatePicker}
                            mode="date"
                            date={formData.panDateOfBirth}
                            onConfirm={(date) =>
                                handleDateChange('panDateOfBirth', date)
                            }
                            onCancel={() => setShowPanDatePicker(false)}
                            maximumDate={new Date()}
                            testID="panDatePicker"
                        />
                        {errors.panDateOfBirth && (
                            <Text style={styles.errorText}>
                                {errors.panDateOfBirth}
                            </Text>
                        )}

                        <TouchableOpacity
                            onPress={() => pickDocument('panDocument')}
                            style={[
                                styles.fileButton,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                                errors.panDocument ? styles.inputError : null,
                            ]}
                        >
                            <Ionicons
                                name="cloud-upload-outline"
                                size={22}
                                color={theme.PRIMARY}
                                style={styles.inputIcon}
                            />
                            <Text
                                style={[
                                    styles.fileButtonText,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                {files.panDocument
                                    ? files.panDocument.name
                                    : 'Upload PAN Document'}
                            </Text>
                        </TouchableOpacity>
                        {errors.panDocument && (
                            <Text style={styles.errorText}>
                                {errors.panDocument}
                            </Text>
                        )}

                        {/* Broker Details Section */}
                        <Text
                            style={[
                                styles.sectionTitle,
                                { color: theme.PRIMARY },
                            ]}
                        >
                            Broker Details
                        </Text>
                        <View
                            style={[
                                styles.inputContainer,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                                errors.experience ? styles.inputError : null,
                            ]}
                        >
                            <Ionicons
                                name="briefcase-outline"
                                size={22}
                                color={
                                    errors.experience ? 'red' : theme.PRIMARY
                                }
                                style={styles.inputIcon}
                            />
                            <TextInput
                                placeholder="Years of Experience"
                                value={formData.experience}
                                onChangeText={(text) =>
                                    handleChange('experience', text)
                                }
                                placeholderTextColor={theme.TEXT_PLACEHOLDER}
                                style={[
                                    styles.input,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                                keyboardType="numeric"
                                accessibilityLabel="Enter years of experience"
                                testID="experience-input"
                            />
                            {formData.experience.length > 0 && (
                                <TouchableOpacity
                                    onPress={() =>
                                        handleChange('experience', '')
                                    }
                                    style={styles.clearButton}
                                >
                                    <Ionicons
                                        name="close-circle"
                                        size={20}
                                        color="#999"
                                    />
                                </TouchableOpacity>
                            )}
                        </View>
                        {errors.experience && (
                            <Text style={styles.errorText}>
                                {errors.experience}
                            </Text>
                        )}

                        <View
                            style={[
                                styles.inputContainer,
                                {
                                    backgroundColor: theme.INPUT_BACKGROUND,
                                    borderColor: theme.INPUT_BORDER,
                                },
                                errors.serviceAreas ? styles.inputError : null,
                            ]}
                        >
                            <Ionicons
                                name="location-outline"
                                size={22}
                                color={
                                    errors.serviceAreas ? 'red' : theme.PRIMARY
                                }
                                style={styles.inputIcon}
                            />
                            <TextInput
                                placeholder="Preferred Service Areas"
                                value={formData.serviceAreas}
                                onChangeText={(text) =>
                                    handleChange('serviceAreas', text)
                                }
                                placeholderTextColor={theme.TEXT_PLACEHOLDER}
                                style={[
                                    styles.input,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                                multiline
                                accessibilityLabel="Enter service areas, comma-separated"
                                testID="serviceAreas-input"
                            />
                            {formData.serviceAreas.length > 0 && (
                                <TouchableOpacity
                                    onPress={() =>
                                        handleChange('serviceAreas', '')
                                    }
                                    style={styles.clearButton}
                                >
                                    <Ionicons
                                        name="close-circle"
                                        size={20}
                                        color="#999"
                                    />
                                </TouchableOpacity>
                            )}
                        </View>
                        {errors.serviceAreas && (
                            <Text style={styles.errorText}>
                                {errors.serviceAreas}
                            </Text>
                        )}

                        {/* Submit Button */}
                        <TouchableOpacity
                            onPress={handleSubmit}
                            style={[
                                styles.submitButton,
                                { shadowColor: theme.PRIMARY },
                            ]}
                            disabled={isLoading}
                            accessibilityLabel="Submit broker application"
                            accessibilityRole="button"
                            testID="submit-button"
                        >
                            <LinearGradient
                                colors={[theme.PRIMARY, theme.SECONDARY]}
                                start={{ x: 0, y: 0 }}
                                end={{ x: 1, y: 0 }}
                                style={styles.submitButtonGradient}
                            >
                                {isLoading ? (
                                    <View style={styles.loadingContainer}>
                                        <ActivityIndicator
                                            color={theme.WHITE}
                                            size="small"
                                        />
                                        <Text
                                            style={[
                                                styles.submitButtonText,
                                                { color: theme.WHITE },
                                            ]}
                                        >
                                            Submitting...
                                        </Text>
                                    </View>
                                ) : (
                                    <Text
                                        style={[
                                            styles.submitButtonText,
                                            { color: theme.WHITE },
                                        ]}
                                    >
                                        Submit Application
                                    </Text>
                                )}
                            </LinearGradient>
                        </TouchableOpacity>
                    </View>
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollContainer: {
        flexGrow: 1,
    },
    backgroundContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: height * 0.6,
        zIndex: -1,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    backgroundOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    contentContainer: {
        flex: 1,
        alignItems: 'center',
    },
    formContainer: {
        width: '90%',
        maxWidth: 400,
        borderRadius: 20,
        padding: 24,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 5,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 8,
        textAlign: 'center',
    },
    subtitle: {
        fontSize: 16,
        marginBottom: 24,
        textAlign: 'center',
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginTop: 16,
        marginBottom: 12,
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 12,
        marginBottom: 8,
        paddingHorizontal: 12,
        height: 56,
    },
    addressInputContainer: {
        height: 80,
    },
    serviceAreaContainer: {
        height: 80,
    },
    inputError: {
        borderColor: 'red',
    },
    errorText: {
        color: 'red',
        fontSize: 12,
        marginBottom: 8,
        marginLeft: 4,
    },
    inputIcon: {
        marginRight: 12,
    },
    input: {
        flex: 1,
        height: '100%',
        fontSize: 16,
    },
    disabledInputContainer: {},
    clearButton: {
        padding: 8,
    },
    dateText: {
        flex: 1,
        fontSize: 16,
        paddingVertical: 16,
    },
    picker: {
        flex: 1,
        height: 56,
    },
    fileButton: {
        borderWidth: 1,
        borderRadius: 12,
        padding: 16,
        marginBottom: 16,
        alignItems: 'center',
    },
    fileButtonText: {
        fontSize: 16,
    },
    submitButton: {
        borderRadius: 12,
        overflow: 'hidden',
        marginTop: 16,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 4,
    },
    submitButtonGradient: {
        paddingVertical: 16,
        alignItems: 'center',
    },
    loadingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    submitButtonText: {
        fontSize: 18,
        fontWeight: 'bold',
        marginLeft: 8,
    },
});
