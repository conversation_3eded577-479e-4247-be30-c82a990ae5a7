import React, { useContext } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    Image,
    KeyboardAvoidingView,
    Platform,
    Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import InputField from '../InputField';
import FilePicker from '../FilePicker';
import BackButton from '../../Components/Shared/BackButton';
import { FormContext, createStyles } from './SiteFormNavigator';

const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB
const MAX_SITE_IMAGES = 10;

const SiteDetailsScreen = ({ navigation }) => {
    const { fields, setFields, errors, theme } = useContext(FormContext);
    const styles = createStyles(theme);

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <ScrollView
                contentContainerStyle={styles.scrollContainer}
                showsVerticalScrollIndicator={false}
            >
                <BackButton color={theme.WHITE} />

                {/* Background Image */}
                <View style={styles.backgroundContainer}>
                    <Image
                        source={require('../../../assets/images/background.png')}
                        style={styles.backgroundImage}
                        resizeMode="cover"
                    />
                    <LinearGradient
                        colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                        style={styles.backgroundOverlay}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 0, y: 1 }}
                    />
                </View>

                {/* Content Container */}
                <View style={styles.contentContainer}>
                    {/* Form Container */}
                    <View
                        style={[
                            styles.formContainer,
                            {
                                shadowColor: theme.SHADOW,
                                backgroundColor: theme.CARD,
                            },
                        ]}
                    >
                        <Text style={[styles.title, { color: theme.PRIMARY }]}>
                            Site Details
                        </Text>
                        <Text
                            style={[
                                styles.subtitle,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Add comprehensive site information
                        </Text>

                        {/* Basic Details Section */}
                        <Text
                            style={[
                                styles.sectionTitle,
                                { color: theme.PRIMARY },
                            ]}
                        >
                            Basic Details
                        </Text>
                        <InputField
                            label="Site name*"
                            value={fields.name}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, name: t }))
                            }
                            placeholder="Enter Site name"
                            icon="business-outline"
                            error={errors?.name}
                        />
                        <InputField
                            label="Address line 1*"
                            value={fields.addressLine1}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, addressLine1: t }))
                            }
                            placeholder="Enter Address line 1"
                            icon="location-outline"
                            error={errors?.addressLine1}
                        />
                        <InputField
                            label="Address line 2"
                            value={fields.addressLine2}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, addressLine2: t }))
                            }
                            placeholder="Enter Address line 2"
                            icon="location-outline"
                            error={errors?.addressLine2}
                        />
                        <InputField
                            label="Landmark"
                            value={fields.landmark}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, landmark: t }))
                            }
                            placeholder="Enter Landmark"
                            icon="flag-outline"
                            error={errors?.landmark}
                        />
                        <InputField
                            label="Pincode*"
                            value={fields.pincode}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, pincode: t }))
                            }
                            keyboardType="numeric"
                            placeholder="Enter Pincode"
                            icon="mail-outline"
                            error={errors?.pincode}
                            maxLength={6}
                        />
                        <InputField
                            label="State"
                            value={fields.state}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, state: t }))
                            }
                            placeholder="Enter State"
                            icon="map-outline"
                            error={errors?.state}
                        />
                        <InputField
                            label="District"
                            value={fields.district}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, district: t }))
                            }
                            placeholder="Enter District"
                            icon="map-outline"
                            error={errors?.district}
                        />
                        <InputField
                            label="Plot area (sqft)*"
                            value={fields.plotArea}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, plotArea: t }))
                            }
                            keyboardType="numeric"
                            placeholder="Enter Plot area"
                            icon="resize-outline"
                            error={errors?.plotArea}
                        />
                        <InputField
                            label="Price"
                            value={fields.price}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, price: t }))
                            }
                            keyboardType="numeric"
                            placeholder="Enter Price"
                            icon="cash-outline"
                            error={errors?.price}
                        />
                        <FilePicker
                            label={`Pick site image${fields.siteImages?.length > 0 ? 's' : ''} (JPG/PNG/PDF, max ${MAX_SITE_IMAGES})`}
                            files={fields.siteImages}
                            setFiles={setFields}
                            keyName="siteImages"
                            maxFiles={MAX_SITE_IMAGES}
                            allowedTypes={ALLOWED_TYPES}
                            maxFileSize={MAX_FILE_SIZE}
                            isMultiple={true}
                        />

                        {/* Next Button */}
                        <TouchableOpacity
                            style={[
                                styles.nextButton,
                                { shadowColor: theme.PRIMARY },
                            ]}
                            onPress={() => navigation.navigate('Location')}
                            activeOpacity={0.8}
                        >
                            <LinearGradient
                                colors={[theme.PRIMARY, theme.SECONDARY]}
                                start={{ x: 0, y: 0 }}
                                end={{ x: 1, y: 0 }}
                                style={styles.nextButtonGradient}
                            >
                                <Text
                                    style={[
                                        styles.nextButtonText,
                                        { color: theme.WHITE },
                                    ]}
                                >
                                    Next
                                </Text>
                            </LinearGradient>
                        </TouchableOpacity>
                    </View>
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    );
};

export default SiteDetailsScreen;
