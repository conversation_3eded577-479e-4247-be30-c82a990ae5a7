import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, StyleSheet, ActivityIndicator, Alert, Image, TouchableOpacity, Dimensions, TextInput } from 'react-native';
import { router } from 'expo-router'; 
import { fetchSites } from '../../../api/sites/GetSites';

const SitesListScreen = () => {
  const [sites, setSites] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchQuery, setSearchQuery] = useState(''); // New state for actual search trigger
  const limit = 1;
  const { width } = Dimensions.get('window');
  const numColumns = Math.floor(width / 180);

  useEffect(() => {
    const getSites = async () => {
      try {
        setLoading(true);
        const filters = { page, limit };
        if (searchQuery) {
          filters.search = searchQuery;
        }
        const data = await fetchSites(filters);
        setSites(data || []);
        setHasMore(data.length === limit);
      } catch (err) {
        setError(err.message);
        Alert.alert('Error', err.message);
      } finally {
        setLoading(false);
      }
    };

    getSites();
  }, [page, searchQuery]);

  const formatAddress = (item) => {
    if (!item) return 'Address not available';
    const parts = [
      item.addressLine1,
      item.addressLine2,
      item.district,
      item.state,
      item.pincode,
    ].filter(part => part && part.trim() !== '');
    return parts.length > 0 ? parts.join(', ') : 'Address not available';
  };

  const handleRetry = () => {
    setError(null);
    setLoading(true);
    setPage(1);
    getSites();
  };

  const handleNextPage = () => {
    if (hasMore) {
      setPage(prevPage => prevPage + 1);
    }
  };

  const handlePreviousPage = () => {
    if (page > 1) {
      setPage(prevPage => prevPage - 1);
    }
  };

  const handleSearch = () => {
    setSearchQuery(searchTerm); // Trigger search with current searchTerm
    setPage(1); // Reset to first page
  };

  const renderItem = ({ item, index }) => (
    <View style={styles.siteItem}>
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => {
          if (item._id) {
           router.push({
            pathname: 'Sites/SiteDetailsScreen',
            params: { siteId: item._id},
          })
          } else {
            Alert.alert('Error', 'Site ID not available');
          }
        }}
        accessibilityRole="button"
        accessibilityLabel={`View details for ${item.name || 'Unnamed Site'}`}
      >
        {item.thumbnail?.imageURL ? (
          <Image
            source={{ uri: item.thumbnail.imageURL }}
            style={styles.thumbnail}
            resizeMode="cover"
            onError={() => Alert.alert('Error', 'Failed to load image')}
          />
        ) : (
          <View style={[styles.thumbnail, styles.placeholderImage]} />
        )}
      </TouchableOpacity>
      <View style={styles.contentContainer}>
        <Text style={styles.siteName} numberOfLines={1} ellipsizeMode="tail">
          {item.name || 'Unnamed Site'}
        </Text>
        <Text style={styles.siteDetails} numberOfLines={2} ellipsizeMode="tail">
          {formatAddress(item)}
        </Text>
        <View style={styles.infoContainer}>
          <Text style={styles.siteDetails}>Area: {item.plotArea || 'N/A'} sq.ft</Text>
          <Text style={styles.sitePrice}>₹{item.price || 'N/A'}</Text>
        </View>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centered}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Available Sites</Text>
      <View style={styles.searchContainer}>
        <TextInput
          style={[styles.searchInput, { flex: 1 }]}
          placeholder="Search sites (e.g., beachfront)"
          value={searchTerm}
          onChangeText={setSearchTerm}
          accessibilityLabel="Search sites"
        />
        <TouchableOpacity
          style={styles.searchButton}
          onPress={handleSearch}
          accessibilityRole="button"
          accessibilityLabel="Search"
        >
          <Text style={styles.searchButtonText}>Search</Text>
        </TouchableOpacity>
      </View>
      <FlatList
        data={sites}
        renderItem={renderItem}
        keyExtractor={(item, index) => item._id?.toString() || `site-${index}`}
        numColumns={numColumns}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={<Text style={styles.emptyText}>No sites available</Text>}
        columnWrapperStyle={styles.columnWrapper}
        showsVerticalScrollIndicator={false}
        key={numColumns}
        ListFooterComponent={
          <View style={styles.footer}>
            <View style={styles.paginationContainer}>
              {page > 1 && (
                <TouchableOpacity
                  style={styles.prevButton}
                  onPress={handlePreviousPage}
                  accessibilityRole="button"
                  accessibilityLabel="Load previous page"
                >
                  <Text style={styles.prevButtonText}>Previous Page</Text>
                </TouchableOpacity>
              )}
              <Text style={styles.pageText}>Page {page}</Text>
              <TouchableOpacity
                style={[styles.nextButton, !hasMore && styles.disabledButton]}
                onPress={handleNextPage}
                disabled={!hasMore}
                accessibilityRole="button"
                accessibilityLabel="Load next page"
              >
                <Text style={styles.nextButtonText}>Next Page</Text>
              </TouchableOpacity>
            </View>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    paddingHorizontal: 10,
    paddingTop: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000',
    textAlign: 'center',
    marginBottom: 10,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  searchInput: {
    height: 40,
    borderColor: '#ddd',
    borderWidth: 1,
    borderRadius: 5,
    paddingHorizontal: 10,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  searchButton: {
    backgroundColor: '#0000ff',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 5,
    marginLeft: 10,
  },
  searchButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  listContent: {
    paddingBottom: 10,
  },
  columnWrapper: {
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  siteItem: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    margin: 5,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  thumbnail: {
    width: '100%',
    height: 100,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  placeholderImage: {
    backgroundColor: '#ccc',
  },
  contentContainer: {
    padding: 10,
  },
  siteName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 5,
  },
  siteDetails: {
    fontSize: 12,
    color: '#555',
    marginBottom: 5,
  },
  sitePrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#0000ff',
  },
  infoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#555',
    marginTop: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#ff0000',
    textAlign: 'center',
    marginBottom: 10,
  },
  loadingText: {
    fontSize: 16,
    color: '#0000ff',
    marginTop: 10,
  },
  retryButton: {
    backgroundColor: '#0000ff',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    marginTop: 10,
  },
  retryButtonText: {
    color: '#fff',
  },
  footer: {
    padding: 10,
    alignItems: 'center',
  },
  paginationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    maxWidth: 400,
  },
  prevButton: {
    backgroundColor: '#0000ff',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    marginRight: 10,
  },
  prevButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  nextButton: {
    backgroundColor: '#0000ff',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    marginLeft: 10,
  },
  nextButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  pageText: {
    fontSize: 16,
    color: '#000',
    fontWeight: 'bold',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
});

export default SitesListScreen;