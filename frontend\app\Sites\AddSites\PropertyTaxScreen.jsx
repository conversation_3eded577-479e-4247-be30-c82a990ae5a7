import React, { useContext } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    Image,
    KeyboardAvoidingView,
    Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import InputField from '../InputField';
import FilePicker from '../FilePicker';
import BackButton from '../../Components/Shared/BackButton';
import { FormContext, createStyles } from './SiteFormNavigator';

const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB

const PropertyTaxScreen = ({ navigation }) => {
    const { fields, setFields, errors, theme } = useContext(FormContext);
    const styles = createStyles(theme);

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <ScrollView
                contentContainerStyle={styles.scrollContainer}
                showsVerticalScrollIndicator={false}
            >
                <BackButton color={theme.WHITE} />

                {/* Background Image */}
                <View style={styles.backgroundContainer}>
                    <Image
                        source={require('../../../assets/images/background.png')}
                        style={styles.backgroundImage}
                        resizeMode="cover"
                    />
                    <LinearGradient
                        colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                        style={styles.backgroundOverlay}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 0, y: 1 }}
                    />
                </View>

                {/* Content Container */}
                <View style={styles.contentContainer}>
                    {/* Form Container */}
                    <View
                        style={[
                            styles.formContainer,
                            {
                                shadowColor: theme.SHADOW,
                                backgroundColor: theme.CARD,
                            },
                        ]}
                    >
                        <Text style={[styles.title, { color: theme.PRIMARY }]}>
                            Property Tax Receipt
                        </Text>
                        <Text
                            style={[
                                styles.subtitle,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            Provide property tax receipt details
                        </Text>

                        {/* Property Tax Details Section */}
                        <Text
                            style={[
                                styles.sectionTitle,
                                { color: theme.PRIMARY },
                            ]}
                        >
                            Property Tax Details
                        </Text>
                        <InputField
                            label="Owner name"
                            value={fields.ptrOwnerName}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, ptrOwnerName: t }))
                            }
                            placeholder="Enter Owner name"
                            icon="person-outline"
                            error={errors?.ptrOwnerName}
                        />
                        <InputField
                            label="Receipt No."
                            value={fields.ptrReciptNo}
                            onChangeText={(t) =>
                                setFields((f) => ({ ...f, ptrReciptNo: t }))
                            }
                            placeholder="Enter Receipt No."
                            icon="receipt-outline"
                            error={errors?.ptrReciptNo}
                        />
                        <FilePicker
                            label="Pick property tax receipt"
                            files={fields.propertyTaxRec}
                            setFiles={setFields}
                            keyName="propertyTaxRec"
                            allowedTypes={ALLOWED_TYPES}
                            maxFileSize={MAX_FILE_SIZE}
                        />

                        {/* Navigation Buttons */}
                        <View style={styles.buttonContainer}>
                            <TouchableOpacity
                                style={styles.backButton}
                                onPress={() => navigation.goBack()}
                                activeOpacity={0.8}
                            >
                                <Text
                                    style={[
                                        styles.backButtonText,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Back
                                </Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[
                                    styles.nextButton,
                                    { shadowColor: theme.PRIMARY },
                                ]}
                                onPress={() => navigation.navigate('Submit')}
                                activeOpacity={0.8}
                            >
                                <LinearGradient
                                    colors={[theme.PRIMARY, theme.SECONDARY]}
                                    start={{ x: 0, y: 0 }}
                                    end={{ x: 1, y: 0 }}
                                    style={styles.nextButtonGradient}
                                >
                                    <Text
                                        style={[
                                            styles.nextButtonText,
                                            { color: theme.WHITE },
                                        ]}
                                    >
                                        Next
                                    </Text>
                                </LinearGradient>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    );
};

export default PropertyTaxScreen;
