import { View, Text, FlatList, TouchableOpacity, Animated } from 'react-native';
import React, { useRef, useContext } from 'react';
import Card from './Card';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useRouter } from 'expo-router';
import { ThemeContext } from '../../../context/ThemeContext';

export default function CategorySection({
    title,
    data,
    onItemPress,
    viewAllRoute,
}) {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const fadeAnim = useRef(new Animated.Value(0)).current;

    React.useEffect(() => {
        Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
        }).start();
    }, [fadeAnim]);

    const handleViewAll = () => {
        if (viewAllRoute) {
            router.push(viewAllRoute);
        }
    };

    return (
        <Animated.View className="mt-2 py-1" style={{ opacity: fadeAnim }}>
            <View className="flex-row justify-between items-center px-4 mb-3">
                <Text className="text-lg font-bold text-primary">{title}</Text>
                {viewAllRoute && (
                    <TouchableOpacity
                        className="flex-row items-center bg-primary/10 px-3 py-1.5 rounded-full"
                        onPress={handleViewAll}
                        accessibilityLabel={`View all ${title}`}
                    >
                        <Text className="text-sm font-semibold text-primary mr-1">
                            View All
                        </Text>
                        <Ionicons
                            name="chevron-forward"
                            size={16}
                            color={theme.PRIMARY}
                        />
                    </TouchableOpacity>
                )}
            </View>
            <FlatList
                data={data}
                keyExtractor={(item) => item.id}
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{ paddingHorizontal: 16 }}
                renderItem={({ item }) => (
                    <Card
                        {...item}
                        onPress={() => onItemPress(item)}
                        onInterestedPress={item.onInterestedPress}
                        accessibilityLabel={`View details for ${item.title || item.name}`}
                    />
                )}
            />
        </Animated.View>
    );
}
