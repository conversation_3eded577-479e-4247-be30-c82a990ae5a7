import React, { useContext } from 'react';
import {
    View,
    Text,
    TextInput,
    StyleSheet,
    TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';

const InputField = ({
    label,
    value,
    onChangeText,
    keyboardType = 'default',
    placeholder,
    icon,
    error,
    multiline = false,
    editable = true,
    maxLength,
    autoCapitalize = 'none',
}) => {
    const { theme } = useContext(ThemeContext);

    return (
        <View style={styles.inputRow}>
            <Text style={[styles.label, { color: theme.TEXT_PRIMARY }]}>
                {label}
            </Text>
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: error
                            ? theme.ERROR
                            : value && value.length > 0
                              ? theme.PRIMARY
                              : theme.INPUT_BORDER,
                    },
                    error ? styles.inputError : null,
                    multiline ? styles.multilineContainer : null,
                    !editable ? styles.disabledContainer : null,
                ]}
            >
                {icon && (
                    <Ionicons
                        name={icon}
                        size={22}
                        color={error ? theme.ERROR : theme.PRIMARY}
                        style={styles.inputIcon}
                    />
                )}
                <TextInput
                    style={[
                        styles.input,
                        {
                            color: editable
                                ? theme.TEXT_PRIMARY
                                : theme.TEXT_SECONDARY,
                        },
                        multiline ? styles.multilineInput : null,
                    ]}
                    value={value}
                    onChangeText={onChangeText}
                    keyboardType={keyboardType}
                    placeholder={placeholder}
                    placeholderTextColor={theme.TEXT_PLACEHOLDER}
                    multiline={multiline}
                    editable={editable}
                    maxLength={maxLength}
                    autoCapitalize={autoCapitalize}
                />
                {value && value.length > 0 && editable && (
                    <TouchableOpacity
                        onPress={() => onChangeText('')}
                        style={styles.clearButton}
                    >
                        <Ionicons
                            name="close-circle"
                            size={20}
                            color={theme.TEXT_SECONDARY}
                        />
                    </TouchableOpacity>
                )}
            </View>
            {error && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {error}
                </Text>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    inputRow: {
        marginBottom: 16,
    },
    label: {
        fontSize: 14,
        fontWeight: '500',
        marginBottom: 8,
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 12,
        paddingHorizontal: 12,
        height: 56,
    },
    multilineContainer: {
        height: 80,
        alignItems: 'flex-start',
        paddingVertical: 12,
    },
    disabledContainer: {
        opacity: 0.7,
    },
    inputError: {
        borderWidth: 2,
    },
    inputIcon: {
        marginRight: 12,
    },
    input: {
        flex: 1,
        fontSize: 16,
        paddingVertical: 0,
    },
    multilineInput: {
        textAlignVertical: 'top',
        paddingTop: 4,
    },
    clearButton: {
        padding: 4,
        marginLeft: 8,
    },
    errorText: {
        fontSize: 14,
        marginTop: 4,
        marginLeft: 4,
    },
});

export default InputField;
